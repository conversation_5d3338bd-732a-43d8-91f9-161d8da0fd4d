import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { BackgroundTasksProvider } from "@/context/BackgroundTasksContext";
import { BackgroundTasksIndicator } from "@/components/BackgroundTasksIndicator";
import NotFound from "@/pages/not-found";
import Home from "@/pages/home";
import AuthPage from "@/pages/auth-page";
import LoginPage from "@/pages/login-page";
import RegisterPage from "@/pages/register-page";
import DashboardPage from "@/pages/dashboard-elegant";
import TestPage from "@/pages/test-page";
import TestPageNew from "@/test-page";
import TestImagePage from "@/pages/test-image-page";
import SafariTestPage from "@/pages/safari-test-page";
import MarketingToolsPage from "@/pages/marketing-tools-page";
import ToolPage from "@/pages/tool-page";
import AIContentHubPage from "@/pages/ai-content-hub-page";
import AIContentHubPageNew from "@/pages/ai-content-hub-page-new";
import AIContentCreatorPage from "@/pages/ai-content-creator-page-new";
import AIContentGeneratorPage from "@/pages/ai-content-generator-page";
import LangFlowDemoPage from "@/pages/langflow-demo-page";
import InstagramCopywriterPage from "@/pages/instagram-copywriter-page";
import SEOXAnalyzerPage from "@/pages/seox-analyzer-page";
import UserProfilePage from "@/pages/user-profile-page";
import EmmaVisualStudioPage from "@/pages/emma-visual-studio-v2";
import VisualEditorPage from "@/pages/visual-editor";
import VideoEditorPage from "@/pages/video-editor-page";
import SimpleVideoGeneratorPage from "@/pages/simple-video-generator-page";
import ImageToVideoPage from "@/pages/image-to-video-page";
import VideoToolsHub from "@/pages/video-tools-hub";
import VisualToolsHub from "@/pages/visual-tools-hub";
import VideoStudioPage from "@/pages/video-studio-page";
import LumaLabsPage from "@/pages/luma-labs-page";
import GoogleVeoPage from "@/pages/google-veo-page";
import RunwayMLPage from "@/pages/runway-ml-page";
import TextToVideoPage from "@/pages/text-to-video-page";
import ShortsGeneratorPage from "@/pages/shorts-generator-page";
import ImageGeneratorPage from "@/pages/image-generator-page";
import ContentPlanner from "@/pages/content-planner";
import EditorProfesional from "@/pages/editor-profesional";
import AIImageEditor from "@/pages/ai-image-editor";
import AIImageEditorSimple from "@/pages/ai-image-editor-simple";
import BackgroundRemoverPage from "@/pages/background-remover-page";
import ReplaceBackgroundPage from "@/pages/replace-background-page";
import SketchToImagePage from "@/pages/sketch-to-image-improved";
import ImageEnhancerPage from "@/pages/image-enhancer-page";
import Generate3DPage from "@/pages/3d-generator-page";
import GenerationStatusTestPage from "@/pages/generation-status-test-page";
import { LangChainFormPage } from "@/pages/langchain-form-page";
import AgentTeamPage from "@/pages/agent-team-page";
import HumanServicesPageNew from "@/pages/human-services-page-new";
import MarwickLanding from "@/pages/marwick-landing";
import MarwickLandingV2 from "@/pages/marwick-landing-v2";
import EmmaTeamV2 from "@/pages/emma-team-v2";
import { AuthProvider } from "@/hooks/use-auth";
import VideoGenerator from "@/pages/video-generator";
import TestAgentsPage from "@/pages/test-agents-page";
import ProfesionalesIA from "@/pages/profesionales-ia";
import SolucionesNegocio from "@/pages/soluciones-negocio";
import ConversationSimulatorPage from "@/pages/conversation-simulator-page";
import EmmaAgenticSeekPage from "@/pages/emma-agenticseek-page";
import StyleReferencePage from "@/pages/style-reference-page";
import StyleTransferPage from "@/pages/style-transfer-page";
import EraseDemo from "@/pages/erase-demo";
import ImageEraseToolPage from "@/pages/image-erase-tool-page";
import LogoGeneratorPage from "@/pages/logo-generator-page";
import InfographicCreatorPage from "@/pages/infographic-creator-page";
import PosterCreatorPage from "@/pages/poster-creator-page";
import MemeCreatorPage from "@/pages/meme-creator-page";
import AdCreatorPage from "@/pages/ad-creator-page";
import AdTypeSelector from "@/pages/ad-type-selector-page";
import AdCreatorEditor from "@/pages/ad-creator-editor-page";
import MockupGeneratorPage from "@/pages/mockup-generator-page";
import MarcaPage from "@/pages/marca-page";
import CrearMarcaPage from "@/pages/crear-marca-page";
import VibeMarketingPage from "@/pages/vibe-marketing-page";
import PolotnoTestPage from "@/pages/polotno-test-page";
import MoodBoardPage from "@/pages/mood-board-page";
import MoodBoardEditorPage from "@/pages/mood-board-editor-page";
import AdsCentralPage from "@/pages/ads-central-page";
import AdsTemplatesPage from "@/pages/ads-templates-page";
import AgentsMarketplacePage from "@/pages/agents-marketplace-page";
import InfluencerGeneratorPage from "@/pages/influencer-generator-page";
import { ChatMessageTest } from "@/components/test/chat-message-test";

function Router() {
  return (
    <Switch>
      {/* Landing marketing */}
      <Route path="/" component={Home} />
      <Route path="/auth" component={AuthPage} />
      <Route path="/landing" component={Home} />
      <Route path="/dashboard" component={DashboardPage} />
      <Route path="/test-page" component={TestPage} />
      <Route path="/test-new" component={TestPageNew} />
      <Route path="/test-image" component={TestImagePage} />
      <Route path="/safari-test" component={SafariTestPage} />
      <Route
        path="/dashboard/herramientas-marketing"
        component={MarketingToolsPage}
      />
      {/* Nueva ruta para el simulador de conversación */}
      <Route path="/dashboard/herramientas/buyer-persona-generator/simulador/:personaId">
        {(params) => <ConversationSimulatorPage personaId={params.personaId} />}
      </Route>

      {/* Rutas específicas para mood-board (deben ir antes de la ruta genérica) */}
      <Route path="/dashboard/herramientas/mood-board/editor/:boardId">
        {(params) => <MoodBoardEditorPage boardId={params.boardId} />}
      </Route>
      <Route path="/dashboard/herramientas/mood-board" component={MoodBoardPage} />

      {/* Ruta genérica para otras herramientas */}
      <Route path="/dashboard/herramientas/:toolId">
        {(params) => <ToolPage toolId={params.toolId} />}
      </Route>
      <Route path="/dashboard/perfil" component={UserProfilePage} />
      <Route path="/ai-content-hub" component={AIContentHubPageNew} />
      <Route path="/ai-content-hub/old" component={AIContentHubPage} />
      <Route path="/ai-content-hub/create" component={AIContentCreatorPage} />
      <Route
        path="/ai-content-generator/:subcategoryId?"
        component={AIContentGeneratorPage}
      />
      <Route path="/langflow-demo" component={LangFlowDemoPage} />
      <Route
        path="/dashboard/herramientas/instagram-copywriter"
        component={InstagramCopywriterPage}
      />
      <Route path="/instagram-copywriter" component={InstagramCopywriterPage} />
      <Route
        path="/dashboard/herramientas/seox-analyzer"
        component={SEOXAnalyzerPage}
      />
      <Route path="/seox-analyzer" component={SEOXAnalyzerPage} />
      {/* Emma Visual Studio - Ruta principal unificada */}
      <Route path="/emma-studio" component={EmmaVisualStudioPage} />
      <Route path="/emma-visual-studio" component={EmmaVisualStudioPage} />
      <Route path="/visual-editor" component={VisualEditorPage} />
      <Route path="/video-editor" component={VideoEditorPage} />
      <Route path="/video-generator" component={VideoGenerator} />
      <Route path="/video-generator-old" component={SimpleVideoGeneratorPage} />
      <Route
        path="/video-generator/simple"
        component={SimpleVideoGeneratorPage}
      />
      <Route
        path="/simple-video-generator"
        component={SimpleVideoGeneratorPage}
      />
      <Route path="/image-generator" component={ImageGeneratorPage} />
      <Route path="/image-to-video" component={ImageToVideoPage} />
      <Route path="/video-tools" component={VideoToolsHub} />
      <Route path="/video-studio" component={VideoStudioPage} />
      <Route path="/video-studio/luma-labs" component={LumaLabsPage} />
      <Route path="/video-studio/google-veo" component={GoogleVeoPage} />
      <Route path="/video-studio/runway-ml" component={RunwayMLPage} />
      <Route path="/video-studio/text-to-video" component={TextToVideoPage} />
      <Route path="/video-studio/shorts-generator" component={ShortsGeneratorPage} />
      <Route path="/visual-tools" component={VisualToolsHub} />
      <Route path="/editor-profesional" component={EditorProfesional} />
      <Route path="/ai-image-editor" component={AIImageEditor} />
      <Route path="/ai-image-editor-simple" component={AIImageEditorSimple} />
      <Route path="/background-remover" component={BackgroundRemoverPage} />
      <Route path="/replace-background" component={ReplaceBackgroundPage} />
      <Route path="/sketch-to-image" component={SketchToImagePage} />
      <Route path="/image-enhancer" component={ImageEnhancerPage} />
      <Route path="/style-reference" component={StyleReferencePage} />
      <Route path="/style-transfer" component={StyleTransferPage} />
      {/* Ruta para la nueva herramienta de borrar objetos */}
      <Route path="/image-erase-tool" component={ImageEraseToolPage} />
      {/* Ruta para la nueva herramienta de generación 3D */}
      <Route path="/3d-generator" component={Generate3DPage} />
      <Route path="/logo-generator" component={LogoGeneratorPage} />
      <Route path="/infographic-creator" component={InfographicCreatorPage} />
      <Route path="/poster-creator" component={PosterCreatorPage} />
      <Route path="/meme-creator" component={MemeCreatorPage} />
      <Route path="/ad-creator" component={AdCreatorPage} />
      <Route path="/ad-creator/select" component={AdTypeSelector} />
      <Route path="/ad-creator/editor/:platform" component={AdCreatorEditor} />
      <Route path="/mockup-generator" component={MockupGeneratorPage} />
      <Route path="/generation-status" component={GenerationStatusTestPage} />
      <Route path="/content-planner" component={ContentPlanner} />
      <Route path="/formulario/:contentTypeId">
        {(params) => <LangChainFormPage contentTypeId={params.contentTypeId} />}
      </Route>
      <Route path="/agentes/:teamId">
        {(params) => <AgentTeamPage teamId={params.teamId} />}
      </Route>
      <Route path="/dashboard/servicios" component={HumanServicesPageNew} />
      <Route path="/dashboard/marca" component={MarcaPage} />
      <Route path="/dashboard/marca/crear" component={CrearMarcaPage} />
      {/* NEW: Ads Central */}
      <Route path="/dashboard/ads-central" component={AdsCentralPage} />
      <Route path="/dashboard/ads-central/templates" component={AdsTemplatesPage} />
      {/* NEW: Influencer Generator */}
      <Route path="/dashboard/influencer-generator" component={InfluencerGeneratorPage} />
      {/* NEW: Agents Marketplace */}
      <Route path="/dashboard/agents-marketplace" component={AgentsMarketplacePage} />
      <Route path="/marwick" component={MarwickLandingV2} />
      <Route path="/marwick/old" component={MarwickLanding} />
      <Route path="/emma-team" component={EmmaTeamV2} />
      <Route path="/test-agents" component={TestAgentsPage} />
      <Route path="/profesionales-ia" component={ProfesionalesIA} />
      <Route path="/soluciones-negocio" component={SolucionesNegocio} />
      {/* NEW: Vibe Marketing */}
      <Route path="/vibe-marketing" component={VibeMarketingPage} />
      {/* NEW: Polotno Studio Test */}
      <Route path="/polotno-test" component={PolotnoTestPage} />
      {/* NEW: Emma AI - Advanced AI Agent System */}
      <Route path="/emma-agenticseek" component={EmmaAgenticSeekPage} />
      <Route path="/emma-ai" component={EmmaAgenticSeekPage} />
      <Route path="/dashboard/emma-ai" component={EmmaAgenticSeekPage} />
      {/* Mantener esta ruta para otras páginas del dashboard que aún no tienen componentes específicos */}
      <Route path="/dashboard/:path*" component={DashboardPage} />
      <Route path="/login" component={LoginPage} />
      <Route path="/register" component={RegisterPage} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <BackgroundTasksProvider>
          <Router />
          <div className="fixed top-4 right-4 z-50">
            <BackgroundTasksIndicator />
          </div>
          <Toaster />
        </BackgroundTasksProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
