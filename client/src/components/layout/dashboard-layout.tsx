import React, { useState, ReactNode } from "react";
import { motion } from "framer-motion";
import {
  Megaphone,
  Wrench,
  Package,
  LucideIcon,
  Home,
  Settings,
  User,
  Bell,
  LogOut,
  Handshake,
  Sparkles,
  ImageIcon,
  PanelTopOpen,
  MessageSquare,
  Share2,
  Bot,
  Brain,
  Search,
  ChevronDown,
  ChevronRight,
  Palette,
  Users,
  Target,
  Video,
} from "lucide-react";
import { useLocation } from "wouter";
import { emmaAiLogo } from "../../assets";

import {
  SidebarProvider,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";

import { CollapseButton } from "@/components/ui/collapse-button";
import { useAuth } from "@/hooks/use-auth";
import { Button } from "@/components/ui/button";

type SidebarItem = {
  icon: LucideIcon;
  label: string;
  path: string;
};

// Definimos un tipo ampliado que incluye submenús
type SidebarItemWithSubItems = SidebarItem & {
  subItems?: Array<{
    icon: LucideIcon;
    label: string;
    path: string;
  }>;
  expanded?: boolean;
};

const mainNavItems: SidebarItemWithSubItems[] = [
  {
    icon: Bot,
    label: "Marketplace de Agentes",
    path: "/dashboard/agents-marketplace",
  },
  {
    icon: PanelTopOpen,
    label: "Emma Visual Studio",
    path: "/emma-visual-studio",
    subItems: [
      {
        icon: Sparkles,
        label: "Hub Visual",
        path: "/visual-tools",
      },
      {
        icon: Video,
        label: "Video Studio",
        path: "/video-studio",
      },
      {
        icon: ImageIcon,
        label: "Editor Profesional",
        path: "/visual-editor",
      },
      {
        icon: Package,
        label: "Generador de Mockups",
        path: "/mockup-generator",
      },

    ],
  },
  {
    icon: Bot,
    label: "Emma AI",
    path: "/emma-ai",
  },
  {
    icon: Megaphone,
    label: "Generación de Contenido",
    path: "/vibe-marketing",
    subItems: [
      {
        icon: Search,
        label: "SEO + LLM Optimizer",
        path: "/vibe-marketing/seo-llm-optimizer",
      },
      {
        icon: Sparkles,
        label: "Generador de Posts",
        path: "/dashboard/herramientas/generador-posts-profesional",
      },
      {
        icon: Users,
        label: "Generación de Influencers",
        path: "/dashboard/influencer-generator",
      },
    ],
  },
  {
    icon: Wrench,
    label: "Herramientas Marketing",
    path: "/dashboard/herramientas-marketing",
    subItems: [
      {
        icon: Palette,
        label: "🎨 Diseño",
        path: "/dashboard/herramientas-marketing/diseno",
      },
      {
        icon: Users,
        label: "👥 Audiencia",
        path: "/dashboard/herramientas-marketing/audiencia",
      },
      {
        icon: Search,
        label: "🔍 SEO",
        path: "/dashboard/herramientas-marketing/seo",
      },
    ],
  },
  {
    icon: Target,
    label: "Ads Central",
    path: "/dashboard/ads-central",
  },
  {
    icon: Handshake,
    label: "Servicios Humanos",
    path: "/dashboard/servicios",
  },
  {
    icon: Brain,
    label: "Marcas",
    path: "/dashboard/marca",
  },
];

interface DashboardLayoutProps {
  children: ReactNode;
  pageTitle?: string;
}

function InnerDashboardLayout({
  children,
  pageTitle = "Dashboard",
}: DashboardLayoutProps) {
  const { user, logoutMutation } = useAuth();
  const [location, navigate] = useLocation();
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const { open, state, toggleSidebar } = useSidebar();

  // Estado para controlar qué menús están expandidos
  const [expandedMenus, setExpandedMenus] = useState<Record<string, boolean>>({});

  // Determinar el elemento seleccionado en base a la ubicación actual
  const selectedItem = location;

  // Función para alternar la expansión de un menú
  const toggleMenuExpansion = (menuPath: string) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuPath]: !prev[menuPath]
    }));
  };

  // Efecto para auto-expandir menús cuando el usuario navega a una página de submenú
  React.useEffect(() => {
    mainNavItems.forEach(item => {
      if (item.subItems) {
        const hasActiveSubItem = item.subItems.some(subItem => subItem.path === selectedItem);
        if (hasActiveSubItem && !expandedMenus[item.path]) {
          setExpandedMenus(prev => ({
            ...prev,
            [item.path]: true
          }));
        }
      }
    });
  }, [selectedItem]);

  const handleLogout = () => {
    if (user) {
      logoutMutation.mutate();
    } else {
      // En modo desarrollo simplemente redirigimos a la página principal
      navigate("/");
    }
  };

  // Esto será llamado por el botón flotante para actualizar el estado
  const handleSidebarToggle = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
    toggleSidebar();
  };

  return (
    <div className="flex min-h-screen w-full bg-gray-100">
      {/* Pasamos el estado y la función al botón flotante */}
      <CollapseButton
        isCollapsed={state === "collapsed"}
        onClick={handleSidebarToggle}
      />

      {/* Contenedor del sidebar con ancho ajustable */}
      <div
        className={`fixed h-full transition-all duration-300 z-20 ${state === "collapsed" ? "w-[70px]" : "w-64"}`}
        style={{ minHeight: "100vh", boxShadow: "2px 0 10px rgba(0,0,0,0.05)" }}
      >
        <div className="border-r border-gray-200 bg-white h-full">
          <div
            className={`transition-all duration-300 ${isSidebarCollapsed ? "p-2" : "p-6"}`}
          >
            <div className="flex items-center justify-center">
              <img
                src={emmaAiLogo}
                alt="Emma AI Logo"
                className={`transition-all duration-300 ${
                  isSidebarCollapsed
                    ? "h-14 w-14 object-contain p-1"
                    : "h-24 w-auto"
                }`}
              />
            </div>
          </div>

          <div className="px-2">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex w-full min-w-0 flex-col gap-1">
                {/* Botón de inicio siempre resaltado */}
                <div className="group/menu-item relative">
                  <Button
                    variant="ghost"
                    className={`
                      w-full ${isSidebarCollapsed ? "justify-center p-2" : "justify-start py-2 px-3"}
                      text-base rounded-lg
                      bg-blue-500 text-white border-2 border-black
                      hover:bg-blue-600 hover:text-white
                    `}
                    style={{
                      boxShadow: "3px 3px 0px 0px rgba(0,0,0,0.8)",
                    }}
                    onClick={() => {
                      navigate("/dashboard");
                    }}
                  >
                    <Home
                      size={20}
                      className={
                        isSidebarCollapsed ? "text-white mx-auto" : "text-white"
                      }
                    />
                    <span className={isSidebarCollapsed ? "hidden" : ""}>
                      Inicio
                    </span>
                  </Button>
                </div>

                {mainNavItems.map((item, index) => (
                  <div className="relative" key={index}>
                    <div className="group/menu-item relative">
                      <Button
                        variant="ghost"
                        className={`
                          w-full ${isSidebarCollapsed ? "justify-center p-2" : "justify-start py-2 px-3"}
                          text-base rounded-lg transition-all
                          ${selectedItem === item.path ? "bg-blue-50 text-blue-600 border border-blue-200" : "text-gray-700 border border-transparent"}
                          hover:bg-blue-50 hover:text-blue-600
                        `}
                        onClick={() => {
                          // Siempre navegar a la página principal del menú
                          navigate(item.path);
                        }}
                      >
                        <div className="flex items-center w-full">
                          <item.icon
                            size={20}
                            className={
                              isSidebarCollapsed
                                ? "text-blue-500 mx-auto"
                                : "text-blue-500"
                            }
                          />
                          <span className={isSidebarCollapsed ? "hidden" : "flex-1 text-left"}>
                            {item.label}
                          </span>
                          {/* Mostrar chevron solo si tiene submenús y no está colapsado */}
                          {!isSidebarCollapsed && item.subItems && item.subItems.length > 0 && (
                            <div
                              className="ml-auto p-1 hover:bg-blue-100 rounded transition-colors cursor-pointer"
                              onClick={(e) => {
                                e.stopPropagation(); // Evitar que se ejecute el onClick del botón padre
                                toggleMenuExpansion(item.path);
                              }}
                            >
                              {expandedMenus[item.path] ? (
                                <ChevronDown size={16} className="text-gray-400" />
                              ) : (
                                <ChevronRight size={16} className="text-gray-400" />
                              )}
                            </div>
                          )}
                        </div>
                      </Button>
                    </div>

                    {/* Renderiza los submenús si existen, no está colapsado el sidebar Y está expandido */}
                    {!isSidebarCollapsed &&
                      item.subItems &&
                      item.subItems.length > 0 &&
                      expandedMenus[item.path] && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.2 }}
                          className="ml-5 mt-1 border-l-2 border-blue-100 pl-2 py-1 overflow-hidden"
                        >
                          {item.subItems.map((subItem, subIndex) => (
                            <div
                              className="group/submenu-item"
                              key={`${index}-${subIndex}`}
                            >
                              <Button
                                variant="ghost"
                                className={`
                                w-full justify-start py-1.5 px-3
                                text-sm rounded-lg transition-all
                                ${selectedItem === subItem.path ? "bg-blue-50 text-blue-600 border border-blue-200" : "text-gray-700 border border-transparent"}
                                hover:bg-blue-50 hover:text-blue-600
                              `}
                                onClick={() => {
                                  navigate(subItem.path);
                                }}
                              >
                                <div className="flex items-center w-full">
                                  <subItem.icon
                                    size={16}
                                    className="text-blue-500 mr-2"
                                  />
                                  <span>{subItem.label}</span>
                                </div>
                              </Button>
                            </div>
                          ))}
                        </motion.div>
                      )}
                  </div>
                ))}
              </div>
            </motion.div>
          </div>

          <div className="mt-auto mb-4 p-2">
            <div className="flex w-full min-w-0 flex-col gap-1">
              <div className="group/menu-item relative">
                <Button
                  variant="ghost"
                  className={`
                    w-full ${isSidebarCollapsed ? "justify-center p-2" : "justify-start py-2 px-3"}
                    text-base rounded-lg transition-all
                    text-gray-700 border border-transparent
                    hover:bg-blue-50 hover:text-blue-600
                  `}
                  onClick={() => {
                    navigate("/dashboard/perfil");
                  }}
                >
                  <User
                    size={20}
                    className={
                      isSidebarCollapsed
                        ? "text-blue-500 mx-auto"
                        : "text-blue-500"
                    }
                  />
                  <span className={isSidebarCollapsed ? "hidden" : ""}>
                    Perfil
                  </span>
                </Button>
              </div>

              <div className="group/menu-item relative">
                <Button
                  variant="ghost"
                  className={`
                    w-full ${isSidebarCollapsed ? "justify-center p-2" : "justify-start py-2 px-3"}
                    text-base rounded-lg transition-all
                    text-gray-700 border border-transparent
                    hover:bg-red-50 hover:text-red-600
                  `}
                  onClick={handleLogout}
                >
                  <LogOut
                    size={20}
                    className={
                      isSidebarCollapsed
                        ? "text-blue-500 mx-auto"
                        : "text-blue-500"
                    }
                  />
                  <span className={isSidebarCollapsed ? "hidden" : ""}>
                    Cerrar Sesión
                  </span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Contenido principal que se ajusta según el sidebar */}
      <div
        className={`transition-all duration-300 ${isSidebarCollapsed ? "ml-[70px]" : "ml-64"}`}
        style={{
          width: "calc(100% - " + (isSidebarCollapsed ? "70px" : "16rem") + ")",
        }}
      >
        <header className="bg-white border-b border-gray-200 p-4 flex justify-between items-center">
          <div className="flex items-center">
            <SidebarTrigger className="border border-gray-200 rounded-lg mr-4 hover:bg-blue-50 hover:text-blue-600 transition-all" />
            <h1 className="text-2xl font-bold text-gray-800">{pageTitle}</h1>
          </div>
          <div className="flex items-center gap-4">
            {/* Botón de acceso directo a Emma Visual Studio */}
            <Button
              onClick={() => navigate("/emma-visual-studio")}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 border border-blue-700"
            >
              <PanelTopOpen className="mr-2 h-4 w-4" />
              Emma Visual Studio
            </Button>

            <Button
              variant="outline"
              className="border border-gray-200 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-all"
              size="icon"
            >
              <Bell size={20} />
            </Button>
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <User size={20} className="text-blue-600" />
              </div>
              <span className="font-medium text-gray-700">
                {user?.username || "Usuario Demo"}
              </span>
            </div>
          </div>
        </header>

        <main className="p-6 bg-gray-50">{children}</main>
      </div>
    </div>
  );
}

// Componente exportado como nombrado
export function DashboardLayout({ children, pageTitle }: DashboardLayoutProps) {
  return (
    <SidebarProvider defaultOpen={true}>
      <InnerDashboardLayout pageTitle={pageTitle}>
        {children}
      </InnerDashboardLayout>
    </SidebarProvider>
  );
}

// Exportación por defecto para compatibilidad con las importaciones existentes
export default DashboardLayout;
